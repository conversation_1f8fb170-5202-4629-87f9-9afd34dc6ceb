{"$schema": "https://biomejs.dev/schemas/1.9.4/schema.json", "vcs": {"enabled": false, "clientKind": "git", "useIgnoreFile": false}, "files": {"ignoreUnknown": false, "ignore": ["**/node_modules/**", "tsconfig.json", ".next/**", "**/out", "**/build"]}, "formatter": {"enabled": true, "indentStyle": "space", "lineWidth": 120, "indentWidth": 2}, "organizeImports": {"enabled": true}, "linter": {"enabled": true, "rules": {"recommended": false, "correctness": {"noUnusedImports": "error", "noUnusedVariables": "error", "useJsxKeyInIterable": "error", "useExhaustiveDependencies": "error"}, "suspicious": {"noDebugger": "error", "noConsoleLog": "error"}}}, "javascript": {"formatter": {"lineWidth": 120, "jsxQuoteStyle": "single", "quoteProperties": "asNeeded", "trailingCommas": "all", "semicolons": "asNeeded", "arrowParentheses": "always", "bracketSpacing": true, "bracketSameLine": false, "quoteStyle": "single", "attributePosition": "auto"}}}