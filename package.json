{"name": "gaas", "version": "0.1.0", "private": true, "type": "module", "scripts": {"dev": "next dev", "build": "next build && node replacer.js", "start": "next start", "biome": "biome check", "typecheck": "tsc --project tsconfig.json --noEmit", "husky": "bun run biome && bun run typecheck", "prepare": "husky"}, "dependencies": {"@radix-ui/react-dialog": "1.1.14", "@radix-ui/react-navigation-menu": "1.2.13", "@radix-ui/react-separator": "^1.1.2", "@radix-ui/react-slot": "^1.1.2", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "embla-carousel-react": "8.6.0", "lucide-react": "0.475.0", "motion": "12.16.0", "next": "15.1.7", "react": "^19.0.0", "react-dom": "^19.0.0", "replace-in-file": "8.3.0", "tailwind-merge": "^3.0.1", "tailwindcss-animate": "^1.0.7"}, "devDependencies": {"@biomejs/biome": "1.9.4", "@types/canvas-confetti": "1.9.0", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "husky": "9.1.7", "postcss": "^8", "tailwindcss": "3.4.17", "typescript": "^5"}}