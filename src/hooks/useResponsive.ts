'use client'

import { useEffect, useState } from 'react'

const breakpoints = ['375', '480', '768', '1024', '1260', '1440', '1920'] as const
type Breakpoint = (typeof breakpoints)[number]

export function useResponsive() {
  const [width, setWidth] = useState<number | null>(null)

  useEffect(() => {
    const update = () => setWidth(window.innerWidth)
    update()
    window.addEventListener('resize', update)
    return () => window.removeEventListener('resize', update)
  }, [])

  const isScreenSmallerOrEqual = (px: Breakpoint): boolean => typeof width === 'number' && width <= Number(px)
  // const isScreenBiggerOrEqual = (px: Breakpoint): boolean => typeof width === 'number' && width >= Number(px)

  return {
    width,
    isScreenSmallerOrEqual,
  }
}
