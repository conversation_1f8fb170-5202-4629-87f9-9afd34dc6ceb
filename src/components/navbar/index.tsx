'use client'

import { useResponsive } from '@/hooks/useResponsive'
import { cn } from '@/utils/cn'
import { links } from '@/utils/links'
import { AnimatePresence, motion } from 'motion/react'
import { useEffect, useState } from 'react'
import { sectionPXClassName } from '../section'
import { Button } from '../ui/button'
import type { NavbarItem } from './types'

const navItems: NavbarItem[] = [
  { text: "WHAT'S INCLUDED", href: '#what-is-included' },
  { text: 'LAPTOPS', href: '#laptops' },
  { text: 'HOW IT WORKS', href: '#how-it-works' },
  { text: 'FAQS', href: '#faqs' },
]

const MotionButton = motion.create(Button)

export function Navbar() {
  const [activeItem, setActiveItem] = useState('')
  const [lastScrollY, setLastScrollY] = useState(0)
  const { isScreenSmallerOrEqual, width } = useResponsive()
  const isOnTop = lastScrollY < 100

  useEffect(() => {
    const controlNavbar = () => setLastScrollY(window.scrollY)

    window.addEventListener('scroll', controlNavbar)
    return () => window.removeEventListener('scroll', controlNavbar)
  }, [])

  useEffect(() => {
    const sections = document.querySelectorAll('section[id]')

    const observer = new IntersectionObserver(
      (entries) => {
        // Find the entry with the largest intersection ratio
        const visibleEntries = entries.filter((entry) => entry.isIntersecting)

        if (visibleEntries.length > 0) {
          const mostVisibleEntry = visibleEntries.reduce((prev, current) =>
            current.intersectionRatio > prev.intersectionRatio ? current : prev,
          )

          setActiveItem((mostVisibleEntry.target as HTMLElement).id)
        } else {
          setActiveItem('')
        }
      },
      {
        rootMargin: '-20% 0px -20% 0px',
      },
    )

    sections.forEach((section) => observer.observe(section))

    return () => {
      observer.disconnect()
    }
  }, [])

  return (
    <>
      {process.env.NODE_ENV === 'development' && (
        <div className='fixed bg-black text-white top-0 left-0 z-[60] p-1 opacity-60'>{width}px</div>
      )}
      <header
        className={cn(
          'grid grid-cols-[max-content,auto] justify-between border-b border-[#d9d9d9] w-full h-14 sticky top-0 z-50 bg-white duration-500 lte-1024:grid-cols-1 lte-1024:grid-rows-2 lte-1024:h-[88px]',
          sectionPXClassName,
          'px-4',
        )}
      >
        <a
          href='#'
          className={cn('flex items-center gap-2.5 w-max', isScreenSmallerOrEqual('1024') && 'justify-center w-full')}
        >
          <img src='./svgs/logo.svg' className='h-9 w-9' alt='OMEN GaaS Logo' />
          <h1 className='font-bold text-[#212121] text-base leading-6'>OMEN Gaming Subscription</h1>
        </a>

        {isScreenSmallerOrEqual('1024') && (
          <div className='bg-[#d9d9d9] w-full h-[1px] absolute left-0 top-[calc(50%+4px)] translate-y-[-50%]' />
        )}

        <div className='flex items-center justify-between gap-[35px]'>
          <motion.nav
            initial={{ opacity: 0, y: -10 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.7 }}
            viewport={{ once: true }}
            className={cn('flex items-center gap-5', 'lte-1024:w-full lte-1024:justify-between lte-1024:gap-0')}
          >
            {navItems.map((item) => {
              const isActive = activeItem === item.href.replace('#', '')

              return (
                <a
                  key={item.text}
                  href={item.href}
                  className={cn(
                    'duration-300 hover:opacity-70 font-bold text-neutral-700 text-xs leading-5 whitespace-nowrap inline-flex flex-col items-start justify-center focus:outline-none cursor-pointer relative',
                    isScreenSmallerOrEqual('1024') && !isActive && 'font-[325]',
                  )}
                >
                  {item.text}
                  <div
                    className={cn(
                      'duration-500 bg-black w-0 h-[2px] bottom-0 left-0 translate-y-[12px]',
                      isScreenSmallerOrEqual('1024') && isActive && 'w-full',
                    )}
                  />
                </a>
              )
            })}
          </motion.nav>

          {!isScreenSmallerOrEqual('1024') && (
            <>
              <span className='bg-[#404040] w-[1px] h-8' />

              <AnimatePresence>
                {isOnTop ? (
                  <motion.div
                    initial={{ opacity: 0, y: -10 }}
                    animate={{ opacity: 1, y: 0 }}
                    exit={{ opacity: 0, y: 10 }}
                    transition={{ duration: 0.7 }}
                    className={cn(
                      'flex flex-col uppercase duration-500',
                      isOnTop ? 'translate-y-0' : '-translate-y-[100px]',
                    )}
                  >
                    <p className='font-medium text-neutral-700 text-xs leading-4 whitespace-nowrap'>
                      NEED HELP SUBSCRIBING?
                    </p>
                    <p className='font-medium leading-6 text-sm whitespace-nowrap'>
                      <span className='text-neutral-700'>Call us </span>
                      <span className='text-black'>1-866-204-8618</span>
                    </p>
                  </motion.div>
                ) : (
                  <MotionButton
                    initial={{ opacity: 0, y: -10 }}
                    animate={{ opacity: 1, y: 0 }}
                    exit={{ opacity: 0, y: 10 }}
                    transition={{ duration: 0.7 }}
                    hideShadow
                    variant='black'
                    className={cn('h-[32px] duration-500', isOnTop ? '-translate-y-[100px]' : 'translate-y-0')}
                    asChild
                  >
                    <a href={links.keylightStore} target='_blank'>
                      Select Laptop
                    </a>
                  </MotionButton>
                )}
              </AnimatePresence>
            </>
          )}
        </div>
      </header>
    </>
  )
}
