import { cn } from '@/utils/cn'
import type { CSSProperties, ReactNode, Ref } from 'react'

type Props = {
  children: ReactNode
  className?: string
  style?: CSSProperties
  id?: string
  ref?: Ref<HTMLDivElement>
}

export const sectionPYClassName = 'py-[150px] lte-1024:py-[100px] lte-480:py-[50px]'
export const sectionPXClassName = 'px-[40px] lte-768:px-[20px]'

export const sectionClassName = cn(sectionPYClassName, sectionPXClassName)

export function Section({ children, className, style, id, ref, ...rest }: Props) {
  return (
    <section {...rest} ref={ref} id={id} className={cn('relative w-full', sectionClassName, className)} style={style}>
      {children}
    </section>
  )
}
