'use client'

import { SectionContent } from '@/components/section/section-content'
import { Button } from '@/components/ui/button'
import { laptops } from '@/helpers/content/store'
import { motion } from 'motion/react'
import { DashTitle } from '../../heading/dash-title'
import { Title } from '../../heading/title'
import { Section } from '../../section'
import { ProductCard } from './product-card'

import { Carousel, CarouselContent, CarouselItem, CarouselNext, CarouselPrevious } from '@/components/carousel'
import { Heading } from '@/components/heading'
import { useResponsive } from '@/hooks/useResponsive'

const MotionButton = motion.create(Button)

export function Laptops() {
  const { isScreenSmallerOrEqual } = useResponsive()

  return (
    <Section className='gap-2.5 py-[150px] bg-[#181818]' id='laptops'>
      <SectionContent>
        <Heading>
          <DashTitle className='text-white'>Choose a gaming laptop that fits your needs</DashTitle>
          <Title>Select your perfect setup</Title>
        </Heading>

        <div className='relative'>
          <Carousel
            opts={{
              align: 'start',
            }}
            className='w-full'
          >
            <CarouselContent className='-ml-4'>
              {laptops.map((laptop) => (
                <CarouselItem key={laptop.title} className='basis-auto'>
                  <ProductCard
                    title={laptop.title}
                    subtitle={laptop.subtitle}
                    banner={laptop.banner}
                    pictures={laptop.pictures}
                    price={laptop.price}
                    bestFor={laptop.bestFor}
                    specs={laptop.specs}
                    outOfStock={Boolean(laptop.outOfStock)}
                    datasheetLink={laptop.datasheetLink}
                  />
                </CarouselItem>
              ))}
            </CarouselContent>

            {isScreenSmallerOrEqual('1024') && (
              <div className='flex items-center justify-center gap-4 mt-8'>
                <CarouselPrevious />
                <CarouselNext />
              </div>
            )}
          </Carousel>
        </div>

        <div className='flex justify-center'>
          <MotionButton
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.7, delay: 0.3 }}
            viewport={{ once: true }}
          >
            SELECT LAPTOP
          </MotionButton>
        </div>
      </SectionContent>
    </Section>
  )
}
