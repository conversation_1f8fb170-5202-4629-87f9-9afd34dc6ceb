'use client'

import { links } from '@/utils/links'
import { motion } from 'motion/react'
import { Heading } from '../../heading'
import { DashTitle } from '../../heading/dash-title'
import { Title } from '../../heading/title'
import { Section } from '../../section'
import { SectionContent } from '../../section/section-content'
import { Button } from '../../ui/button'
import { FreeTrialModal } from './free-trial-modal'

const MotionButton = motion.create(Button)

export function FreeTrial() {
  return (
    <Section className='py-[150px] bg-[#181818] '>
      <SectionContent>
        <Heading>
          <DashTitle className='text-white'>Risk-Free trial</DashTitle>
          <Title>Try it for 30 days, risk-free.</Title>
        </Heading>

        <div className='space-y-[20px]'>
          <motion.p
            initial={{ opacity: 0, x: -30 }}
            whileInView={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.7, delay: 0.3 }}
            viewport={{ once: true }}
            className='text-white text-xl font-[450]'
          >
            If you decide it’s not for you, simply cancel within the 30-day trial period and return the OMEN gaming
            laptop and accessories for a full refund.<sup>5</sup>
          </motion.p>

          <FreeTrialModal
            trigger={
              <motion.button className='text-white text-xl font-[450] flex items-center gap-2'>
                How the 30-day trial works
                <img src='./svgs/icons/chevron-right.svg' className='w-[16px] h-[16px]' />
              </motion.button>
            }
          />
        </div>

        <MotionButton
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.7, delay: 0.3 }}
          viewport={{ once: true }}
          asChild
        >
          <a href={links.keylightStore} target='_blank'>
            Select laptop
          </a>
        </MotionButton>
      </SectionContent>
    </Section>
  )
}
