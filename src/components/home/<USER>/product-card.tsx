'use client'

import { Link } from '@/components/link'
import { Button } from '@/components/ui/button'
import { useResponsive } from '@/hooks/useResponsive'
import { cn, dualGradientText } from '@/utils/cn'
import { formatCurrency } from '@/utils/currency'
import { useState } from 'react'
import { ProductModal } from './product-modal'
import { SpecItem } from './spec-item'

type ProductProps = {
  ref: React.Ref<HTMLDivElement>
  closedHeight: string
  title: string
  subtitle: string
  banner: string
  pictures: string[]
  price: number
  specs: {
    closed: string[]
    open: string[]
  }
  bestFor?: string
  outOfStock: boolean
  datasheetLink: string
  className?: string
}

export function ProductCard({
  ref,
  title,
  subtitle,
  banner,
  price,
  bestFor,
  specs,
  outOfStock,
  pictures,
  datasheetLink,
  className,
  closedHeight,
}: ProductProps) {
  const [showAllSpecs, setShowAllSpecs] = useState(false)
  const { isScreenSmallerOrEqual } = useResponsive()

  return (
    <div
      ref={ref}
      className={cn(
        'bg-white relative p-[15px] text-[#212121] flex flex-col z-0 justify-between w-[385px]',
        isScreenSmallerOrEqual('1440') && 'w-[375px]',
        isScreenSmallerOrEqual('375') && 'w-[335px]',
        className,
      )}
      style={{ height: showAllSpecs ? '100%' : closedHeight }}
    >
      {outOfStock && <div className='absolute left-0 top-0 w-full z-0 h-full bg-[#212121] opacity-40' />}
      <div>
        <p className='font-bold text-[25px]'>{title}"</p>
        <p className='text-base font-[325]'>{subtitle}</p>

        <img src={banner} alt={`${title} laptop`} className='my-[10px]' />

        {outOfStock && (
          <p className='left-[50%] translate-x-[-50%] translate-y-[-40px] absolute font-bold text-[15px] uppercase text-[#FF0000] z-50 bg-white rounded-[3px] h-[28px] px-[10px] flex items-center justify-center w-[158px] mx-auto'>
            OUT OF STOCK
          </p>
        )}

        <ProductModal
          trigger={
            <button className='flex items-center justify-center w-full gap-[5px] px-2.5 leading-[10px]'>
              <img src='./svgs/store/gallery.svg' alt='Gallery icon' />
              <span className='font-[450] text-[10px] text-left'>
                MORE <br />
                PHOTOS
              </span>
            </button>
          }
          title={title}
          subtitle={subtitle}
          price={price}
          color='Black'
          pictures={pictures}
        />

        <div className='text-black text-right'>
          <span className='font-bold text-[25px]'>{formatCurrency(price)}</span>
          <br />
          <span className='font-[400] text-[20px]'>
            per month<sup>2</sup>
          </span>
        </div>

        <div className='h-[1px] w-full bg-[#404040] my-[15px]' />

        {bestFor && <p className={cn('font-[500] text-[16px]', dualGradientText)}>{bestFor}</p>}

        <ul>
          {(showAllSpecs ? specs.open : specs.closed).map((spec, specIndex) => (
            <SpecItem key={`default_spec_${specIndex}}`}>{spec}</SpecItem>
          ))}
        </ul>
      </div>

      <div className='mt-[46px] space-y-[16px]'>
        {showAllSpecs && (
          <Button variant='gradient' className='text-base font-[450] capitalize h-[28px] w-full gap-[8px]' hideShadow>
            <img src='./svgs/icons/external-link.svg' alt='External link icon' />
            <Link href={datasheetLink} className='text-base text-inherit'>
              View datasheet
            </Link>
          </Button>
        )}

        <button
          onClick={() => setShowAllSpecs((p) => !p)}
          className={cn('flex items-center justify-center gap-[4px] w-full bg-white')}
        >
          <span className='text-neutral-700 font-[450] leading-[10px] text-[10px]'>SEE ALL SPECS</span>
          <img
            src='./svgs/icons/chevron-down-product.svg'
            className={cn('duration-500', showAllSpecs ? 'rotate-180' : 'rotate-0')}
          />
        </button>
      </div>
    </div>
  )
}
