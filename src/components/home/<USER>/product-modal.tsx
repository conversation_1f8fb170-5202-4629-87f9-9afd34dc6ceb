import { Modal } from '@/components/modal'
import { cn } from '@/utils/cn'
import { formatCurrency } from '@/utils/currency'
import { type ReactNode, useState } from 'react'

type Props = {
  trigger: ReactNode
  pictures: string[]
  title: string
  subtitle: string
  price: number
  color: string
}

export function ProductModal({ trigger, pictures, title, subtitle, price, color }: Props) {
  const [currentPictureIndex, setCurrentPictureIndex] = useState(0)

  return (
    <Modal trigger={trigger}>
      <div className='grid  items-center gap-[40px] grid-cols-[auto,328px] lte-1024:grid-cols-1 lte-1024:gap-[15px]'>
        <div className='grid grid-cols-[max-content,auto,max-content] items-center'>
          <button
            onClick={() => {
              setCurrentPictureIndex((p) => (p === 0 ? pictures.length - 1 : p - 1))
            }}
          >
            <img src='./svgs/icons/box-arrow-gradient.svg' alt='Previous image' />
          </button>

          <div>
            <img src={pictures[currentPictureIndex]} alt='Laptop image' className='w-full' />
            <div className='flex items-center justify-center gap-[8px]'>
              {pictures.map((picture, index) => (
                <button
                  key={picture}
                  onClick={() => setCurrentPictureIndex(index)}
                  className={cn(
                    'w-[6px] h-[6px] rounded-full duration-500',
                    index === currentPictureIndex ? 'bg-[#FF0100]' : 'bg-[#D9D9D9]',
                  )}
                ></button>
              ))}
            </div>
          </div>

          <button
            onClick={() => {
              setCurrentPictureIndex((p) => (p === pictures.length - 1 ? 0 : p + 1))
            }}
          >
            <img src='./svgs/icons/box-arrow-gradient.svg' className='rotate-180' alt='Next image' />
          </button>
        </div>

        <div>
          <p className='text-[#212121] text-[25px] flex justify-between items-center gap-[25px] font-bold'>
            <span>{title}"</span>
            <span>{formatCurrency(price)}</span>
          </p>

          <p className='flex justify-between gap-[25px]'>
            <span>{subtitle}</span>
            <span className='text-end'>
              per month <sup>2</sup>
            </span>
          </p>

          <p className='pb-[30px]'>Color: {color}</p>

          <div className='flex gap-[16px]'>
            {pictures.map((picture, index) => (
              <button key={picture} onClick={() => setCurrentPictureIndex(index)}>
                <img src={picture} alt='Laptop image' className='h-[70px] w-[70px]' />
              </button>
            ))}
          </div>
        </div>
      </div>
    </Modal>
  )
}
