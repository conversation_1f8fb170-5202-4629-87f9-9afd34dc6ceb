import type { Disclaimer } from '@/helpers/content/disclaimers/types'

export function DisclaimerItem({ title, list, className }: { title: string; list: Disclaimer[]; className?: string }) {
  return (
    <div className={className}>
      <h2 className='border-b border-[#A3A3A3] text-[#212121] text-base font-bold uppercase pb-[15px] mb-[34px]'>
        {title}
      </h2>
      <ul className='space-y-2.5'>
        {list.map((item) => (
          <li key={item.prefix} className='flex gap-2'>
            <span className='font-bold lowercase'>{item.prefix}. </span>
            {item.render}
          </li>
        ))}
      </ul>
    </div>
  )
}
