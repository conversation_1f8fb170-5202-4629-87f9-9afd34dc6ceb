'use client'

import { howItWorks } from '@/helpers/content/how-it-works'
import { useResponsive } from '@/hooks/useResponsive'
import { cn } from '@/utils/cn'
import { links } from '@/utils/links'
import { motion } from 'motion/react'
import { ReactNode } from 'react'
import { Heading } from '../heading'
import { DashTitle } from '../heading/dash-title'
import { Title } from '../heading/title'
import { Section, sectionClassName } from '../section'
import { SectionContent } from '../section/section-content'
import { Button } from '../ui/button'

function AnimatedImage({ src, alt, className }: { src: string; alt: string; className?: string }) {
  return (
    <motion.img
      initial={{ opacity: 0 }}
      whileInView={{ opacity: 1 }}
      transition={{ duration: 0.7 }}
      viewport={{ once: true }}
      src={src}
      alt={alt}
      className={className}
    />
  )
}

function TextContent({ children, className }: { children: ReactNode; className?: string }) {
  return (
    <div
      className={cn(
        sectionClassName,
        'px-[16px] py-[13px] lte-1260:py-[100px] lte-1260:px-[40px] space-y-[50px] lte-480:space-y-[30px]',
        className,
      )}
    >
      {children}
    </div>
  )
}

export function HowItWorks() {
  const { isScreenSmallerOrEqual } = useResponsive()

  return (
    <Section className='bg-[#181818] text-white lte-1260:p-0 lte-1024:p-0 lte-768:p-0 lte-480:pt-0'>
      <SectionContent>
        {!isScreenSmallerOrEqual('1260') && (
          <>
            <AnimatedImage
              src='./svgs/corner-gradient.svg'
              className='absolute right-0 top-0 rotate-0'
              alt='Corner gradient'
            />
            <AnimatedImage
              src='./svgs/corner-gradient.svg'
              className='absolute left-0 bottom-0 rotate-180'
              alt='Corner gradient'
            />
          </>
        )}

        <div className='grid grid-cols-2 items-center lte-1260:grid-cols-1 gap-y-[100px] lte-1260:gap-y-[0px]'>
          <AnimatedImage
            src='./images/general/how-laptop-and-hands.jpg'
            alt='Man using laptop and mouse'
            className='w-full'
          />
          <TextContent>
            <Heading>
              <DashTitle className='text-white'>subscription</DashTitle>
              <Title>How It Works</Title>
            </Heading>

            <ul className='text-white space-y-[20px] lte-1260:space-y-[25px]'>
              {howItWorks.map((item, index) => (
                <li
                  key={`how-it-works_${index}_${item.title}`}
                  className='grid grid-cols-[max-content,auto] items-center gap-2'
                >
                  <span className='font-bold text-[60px] leading-[60px] text-center w-[90px]'>{index + 1}.</span>
                  <div className='text-sm space-y-3'>
                    <h3 className='font-bold text-base uppercase'>{item.title}</h3>
                    <>{item.description}</>
                  </div>
                </li>
              ))}
            </ul>

            <Button asChild>
              <a href={links.keylightStore} target='_blank'>
                SELECT LAPTOP
              </a>
            </Button>
          </TextContent>
          <TextContent className='lte-1260:order-4'>
            <Heading>
              <DashTitle className='text-white'>upgrades</DashTitle>
              <Title>How to upgrade your gaming laptop</Title>
            </Heading>

            <div>
              <p className='text-white text-sm'>
                Never fall behind the gaming curve again. Every 1 year, OMEN Gaming Subscription allows you to trade in
                your current device for the newest model, ensuring your access to cutting-edge processors,
                next-generation graphics cards, and breakthrough display technology. Simply log in to initiate your
                upgrade and choose your new tech.
              </p>
              <ul className='list-disc space-y-4 mt-4 pl-6 text-white'>
                <li>
                  Stay at the forefront of gaming technology so you can experience the latest AAA titles at their
                  absolute best.
                </li>

                <li>No more saving up for years to upgrade—it's built into your subscription.  </li>

                <li>
                  Expert technical support included throughout your subscription.<sup>3</sup>
                </li>
              </ul>
            </div>

            <Button asChild>
              <a href={links.keylightStore} target='_blank'>
                Select laptop
              </a>
            </Button>
          </TextContent>
          <AnimatedImage
            src='./images/general/how-dual-laptops.jpg'
            alt='Dual laptops over a table'
            className='w-full'
          />
        </div>
      </SectionContent>
    </Section>
  )
}
