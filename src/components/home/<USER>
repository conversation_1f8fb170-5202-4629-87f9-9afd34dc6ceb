'use client'

import { useResponsive } from '@/hooks/useResponsive'
import { cn } from '@/utils/cn'
import { links } from '@/utils/links'
import { motion } from 'motion/react'
import { Heading } from '../heading'
import { Title } from '../heading/title'
import { Section, sectionClassName } from '../section'
import { SectionContent } from '../section/section-content'
import { Button } from '../ui/button'

const MotionButton = motion.create(Button)

export function EnhaceYourSetup() {
  const { isScreenSmallerOrEqual } = useResponsive()

  return (
    <Section
      className={cn('bg-cover bg-center bg-no-repeat bg-[#970512]', isScreenSmallerOrEqual('1024') && 'all:p-0')}
      style={{
        backgroundImage: isScreenSmallerOrEqual('1024') ? '' : 'url(./images/general/setup.jpg)',
      }}
    >
      {isScreenSmallerOrEqual('1260') && (
        <motion.img
          initial={{ opacity: 0, filter: 'blur(10px)' }}
          whileInView={{ opacity: 1, filter: 'blur(0px)' }}
          transition={{ duration: 0.7 }}
          viewport={{ once: true }}
          src={'./images/general/setup-mobile.jpg'}
          className='hidden lte-1024:block w-full'
        />
      )}
      <SectionContent className={cn(isScreenSmallerOrEqual('1024') && cn(sectionClassName, 'all:pt-0'))}>
        <Heading>
          <Title
            className='text-white max-w-[730px] lte-1440:max-w-[680px] lte-1260:max-w-[690px] lte-1024:max-w-max'
            isBigger
          >
            Enhance your setup with optional gaming gear
          </Title>
        </Heading>

        <motion.p
          initial={{ opacity: 0, x: -30 }}
          whileInView={{ opacity: 1, x: 0 }}
          transition={{ duration: 0.7, delay: 0.3 }}
          viewport={{ once: true }}
          className='text-xl leading-[25px] text-white w-full flex flex-col font-[450]'
        >
          <span> Choose from our most popular OMEN monitors and HyperX </span>
          <span>accessories designed to sync effortlessly.</span>
        </motion.p>

        <MotionButton
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.7, delay: 0.3 }}
          viewport={{ once: true }}
          asChild
        >
          <a href={links.keylightStore} target='_blank'>
            get started
          </a>
        </MotionButton>
      </SectionContent>
    </Section>
  )
}
