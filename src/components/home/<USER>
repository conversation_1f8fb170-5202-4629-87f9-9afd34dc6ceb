'use client'

import { includedItems } from '@/helpers/content/what-is-included'
import { cn, dualGradientText } from '@/utils/cn'
import { links } from '@/utils/links'
import { motion } from 'motion/react'
import { Heading } from '../heading'
import { DashTitle } from '../heading/dash-title'
import { Title } from '../heading/title'
import { Section } from '../section'
import { SectionContent } from '../section/section-content'
import { Button } from '../ui/button'

const MotionButton = motion.create(Button)

export function WhatIsIncluded() {
  return (
    <Section id='what-is-included'>
      <SectionContent>
        <Heading>
          <DashTitle>What’s included in the OMEN Gaming Subscription </DashTitle>
          <Title>YOUR GAMING NEEDS COVERED IN ONE PLAN</Title>
        </Heading>

        <div
          className={cn(
            'grid grid-cols-4 lte-1024:grid-cols-2 lte-480:grid-cols-1 gap-[24px] lte-1024:gap-[40px] w-full',
          )}
        >
          {includedItems.map((feature, index) => (
            <motion.div
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.7, delay: 0.3 * index }}
              viewport={{ once: true }}
              key={feature.title}
              className='flex items-start gap-2.5 p-0'
            >
              <img className='w-[46px] h-[32px] object-contain' alt={feature.title} src={feature.icon} />
              <div className='flex flex-col gap-2.5'>
                <h3 className='text-[#212121] text-base font-bold uppercase'>{feature.title}</h3>
                <div className='font-[450] leading-[18px] text-sm'>{feature.description}</div>
              </div>
            </motion.div>
          ))}
        </div>

        <div className='flex flex-col items-center justify-center gap-[15px]'>
          <motion.p
            initial={{ opacity: 0, y: -30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.7 }}
            viewport={{ once: true }}
            className={cn('uppercase font-bold text-base', dualGradientText)}
          >
            Try it for 30 days risk-free<sup className='text-[#FFB300]'>5</sup>
          </motion.p>
          <MotionButton
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.7, delay: 0.3 }}
            viewport={{ once: true }}
            variant='gradient'
            asChild
          >
            <a href={links.keylightStore} target='_blank'>
              SELECT LAPTOP
            </a>
          </MotionButton>
        </div>
      </SectionContent>
    </Section>
  )
}
