'use client'

import { modalSteps } from '@/helpers/content/how-it-works'
import { addonsNonReturnFees, laptops, laptopsNonReturnFees } from '@/helpers/content/laptopsAndAddons'
import { formatCurrency } from '@/utils/currency'
import { links } from '@/utils/links'
import { motion } from 'motion/react'
import { Heading } from '../heading'
import { DashTitle } from '../heading/dash-title'
import { Title } from '../heading/title'
import { Modal } from '../modal'
import { Section } from '../section'
import { SectionContent } from '../section/section-content'
import { SimpleTable } from '../simple-table'
import { Button } from '../ui/button'

const MotionButton = motion.create(Button)

export function FreeTrial() {
  return (
    <Section className='py-[150px] bg-[#181818] '>
      <SectionContent>
        <Heading>
          <DashTitle className='text-white'>Risk-Free trial</DashTitle>
          <Title>Try it for 30 days, risk-free.</Title>
        </Heading>

        <motion.p
          initial={{ opacity: 0, x: -30 }}
          whileInView={{ opacity: 1, x: 0 }}
          transition={{ duration: 0.7, delay: 0.3 }}
          viewport={{ once: true }}
          className='text-white text-xl font-[450]'
        >
          If you decide it’s not for you, simply cancel within the 30-day trial period and return the OMEN gaming laptop
          and accessories for a full refund.<sup>5</sup>
        </motion.p>

        <Modal
          trigger={
            <motion.button className='text-white text-xl font-[450] flex items-center gap-2'>
              How the 30-day trial works
              <img src='./svgs/icons/chevron-right.svg' className='w-[16px] h-[16px]' />
            </motion.button>
          }
        >
          <div className='space-y-[50px]'>
            <div>
              <Title className='mb-[20px]'>How it works</Title>

              <ul>
                {modalSteps.steps.map((step) => (
                  <li key={step.title} className='grid grid-cols-[max-content_auto] items-center gap-[16px]'>
                    <img src={step.icon} />

                    <div>
                      <h3 className='text-lg font-bold uppercase'>{step.title}</h3>
                      <div className='text-base font-[450] text-[#666666]'>{step.description}</div>
                    </div>
                  </li>
                ))}
              </ul>
            </div>

            <div className='space-y-[20px]'>
              <p className='uppercase font-bold text-lg text-[#404040]'>
                If you cancel after 30 days, cancellation fees apply
              </p>

              <div className='grid grid-cols-4 lte-1024:grid-cols-1'>
                {laptops.map((item) => (
                  <SimpleTable
                    key={`faq_table_cancellation_${item.title}`}
                    variant='light'
                    title={item.title}
                    legend='Cancellation Fee after 30 days'
                    subtitle={`${formatCurrency(item.price)} monthly payment`}
                    rows={item.fees.map((fee, index) => ({
                      left: `${index + 1}${1 + index === item.fees.length ? '+' : ''} month${index > 0 ? 's' : ''}`,
                      right: index === 0 ? formatCurrency(fee).split('.')[0] : formatCurrency(fee),
                    }))}
                  />
                ))}
              </div>
            </div>

            <div className='space-y-[20px]'>
              <p className='uppercase font-bold text-lg text-[#404040]'>
                Non-return fees apply if you do not return the gaming laptop and any accessories.
              </p>

              <div className='grid grid-cols-2'>
                <SimpleTable
                  title='LAPTOP'
                  variant='light'
                  rows={laptopsNonReturnFees.map((item) => ({
                    left: item.name,
                    right: '',
                  }))}
                />

                <SimpleTable
                  title='Non-return fee'
                  variant='light'
                  rows={laptopsNonReturnFees.map((item) => ({
                    left: formatCurrency(item.amount).split('.')[0],
                    right: '',
                  }))}
                />
              </div>

              <div className='grid grid-cols-2'>
                <SimpleTable
                  title='Add-Ons'
                  variant='light'
                  rows={addonsNonReturnFees.map((item) => ({
                    left: item.name,
                    right: '',
                  }))}
                />

                <SimpleTable
                  title='Non-return fee'
                  variant='light'
                  rows={addonsNonReturnFees.map((item) => ({
                    left: formatCurrency(item.amount),
                    right: '',
                  }))}
                />
              </div>
            </div>
          </div>
        </Modal>

        <MotionButton
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.7, delay: 0.3 }}
          viewport={{ once: true }}
          asChild
        >
          <a href={links.keylightStore} target='_blank'>
            Select laptop
          </a>
        </MotionButton>
      </SectionContent>
    </Section>
  )
}
