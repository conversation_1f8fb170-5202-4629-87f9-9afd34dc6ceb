'use client'

import { useResponsive } from '@/hooks/useResponsive'
import { cn } from '@/utils/cn'
import { links } from '@/utils/links'
import { motion } from 'motion/react'
import { Heading } from '../heading'
import { Title } from '../heading/title'
import { Section, sectionClassName } from '../section'
import { SectionContent } from '../section/section-content'
import { Button } from '../ui/button'

const MotionButton = motion.create(Button)

export function Hero() {
  const { isScreenSmallerOrEqual } = useResponsive()

  return (
    <Section
      className={cn('bg-cover bg-center bg-no-repeat bg-[#181818]', isScreenSmallerOrEqual('1024') && 'all:p-0')}
      style={{
        backgroundImage: isScreenSmallerOrEqual('1024') ? '' : 'url(./images/general/hero.jpg)',
      }}
    >
      <motion.img
        src='./images/general/hero-mobile.jpg'
        className='hidden lte-1024:block w-full'
        initial={{ opacity: 0, filter: 'blur(10px)' }}
        whileInView={{ opacity: 1, filter: 'blur(0px)' }}
        transition={{ duration: 0.7 }}
        viewport={{ once: true }}
      />
      <SectionContent className={cn(isScreenSmallerOrEqual('1024') && cn(sectionClassName, 'all:pt-0'))}>
        <Heading>
          <Title
            className={cn(
              'max-w-[705px] lte-1440:max-w-[675px] lte-1260:max-w-[650px] lte-1024:max-w-[740px] lte-768:max-w-[728px] lte-480:max-w-max',
              'text-[70px] leading-[75px] lte-1024:text-[45px] lte-1024:leading-[50px] lte-375:text-[30px] lte-375:leading-[100%]',
            )}
          >
            ELEVATE YOUR GAME WITH ONE SUBSCRIPTION
          </Title>
        </Heading>

        <motion.p
          initial={{ opacity: 0, x: -30 }}
          whileInView={{ opacity: 1, x: 0 }}
          transition={{ duration: 0.7, delay: 0.3 }}
          viewport={{ once: true }}
          className='text-[20px] leading-[25px] text-white w-full font-[325] max-w-[650px] lte-1024:max-w-[760px]'
        >
          Get a new OMEN gaming laptop and choose to add HyperX accessories, all backed by continuous coverage
          <sup>3</sup> and the ability to upgrade after 1 year, for one monthly price.<sup>1</sup>
        </motion.p>

        <MotionButton
          initial={{ opacity: 0, x: -30 }}
          whileInView={{ opacity: 1, x: 0 }}
          transition={{ duration: 0.7 }}
          viewport={{ once: true }}
          asChild
        >
          <a href={links.keylightStore} target='_blank'>
            SELECT LAPTOP
          </a>
        </MotionButton>
      </SectionContent>
    </Section>
  )
}
