'use client'

import { Title } from '@/components/heading/title'
import { Modal } from '@/components/modal'
import { SimpleTable } from '@/components/simple-table'
import { modalSteps } from '@/helpers/content/how-it-works'
import { addonsNonReturnFees, laptops, laptopsNonReturnFees } from '@/helpers/content/laptopsAndAddons'
import { formatCurrency } from '@/utils/currency'
import { ReactNode } from 'react'

export function FreeTrialModal({ trigger }: { trigger: ReactNode }) {
  return (
    <Modal trigger={trigger}>
      <div className='space-y-[50px]'>
        <div>
          <Title className='mb-[20px]'>How it works</Title>

          <ul className='space-y-[20px]'>
            {modalSteps.steps.map((step) => (
              <li key={step.title} className='grid grid-cols-[max-content_auto] items-center gap-[16px]'>
                <img src={step.icon} />

                <div>
                  <h3 className='text-lg font-bold uppercase'>{step.title}</h3>
                  <div className='text-base font-[450] text-[#666666]'>{step.description}</div>
                </div>
              </li>
            ))}
          </ul>
        </div>

        <div className='space-y-[20px]'>
          <p className='uppercase font-bold text-lg text-[#404040]'>
            If you cancel after 30 days, cancellation fees apply
          </p>

          <div className='grid grid-cols-4 lte-1024:grid-cols-1'>
            {laptops.map((item) => (
              <SimpleTable
                key={`faq_table_cancellation_${item.title}`}
                variant='light'
                title={item.title}
                legend='Cancellation Fee after 30 days'
                subtitle={`${formatCurrency(item.price)} monthly payment`}
                rows={item.fees.map((fee, index) => ({
                  left: `${index + 1}${1 + index === item.fees.length ? '+' : ''} month${index > 0 ? 's' : ''}`,
                  right: index === 0 ? formatCurrency(fee).split('.')[0] : formatCurrency(fee),
                }))}
              />
            ))}
          </div>
        </div>

        <div className='space-y-[20px]'>
          <p className='uppercase font-bold text-lg text-[#404040]'>
            Non-return fees apply if you do not return the gaming laptop and any accessories.
          </p>

          <div className='grid grid-cols-2'>
            <SimpleTable
              title='LAPTOP'
              variant='light'
              rows={laptopsNonReturnFees.map((item) => ({
                left: item.name,
                right: '',
              }))}
            />

            <SimpleTable
              title='Non-return fee'
              variant='light'
              rows={laptopsNonReturnFees.map((item) => ({
                left: formatCurrency(item.amount).split('.')[0],
                right: '',
              }))}
            />
          </div>

          <div className='grid grid-cols-2'>
            <SimpleTable
              title='Add-Ons'
              variant='light'
              rows={addonsNonReturnFees.map((item) => ({
                left: item.name,
                right: '',
              }))}
            />

            <SimpleTable
              title='Non-return fee'
              variant='light'
              rows={addonsNonReturnFees.map((item) => ({
                left: formatCurrency(item.amount),
                right: '',
              }))}
            />
          </div>
        </div>
      </div>
    </Modal>
  )
}
