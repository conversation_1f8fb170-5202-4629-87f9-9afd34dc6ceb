'use client'

import { motion } from 'motion/react'
import type { ReactNode } from 'react'

export function Heading({ children }: { children: ReactNode }) {
  return (
    <motion.div
      initial={{ opacity: 0, y: -30 }}
      whileInView={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.7 }}
      viewport={{ once: true }}
      className='space-y-[10px] text-left'
    >
      {children}
    </motion.div>
  )
}
