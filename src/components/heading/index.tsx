'use client'

import { cn } from '@/utils/cn'
import { motion } from 'motion/react'
import type { ReactNode } from 'react'

export function Heading({ children, className }: { children: ReactNode; className?: string }) {
  return (
    <motion.div
      initial={{ opacity: 0, y: -30 }}
      whileInView={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.7 }}
      viewport={{ once: true }}
      className={cn('space-y-[10px] text-left', className)}
    >
      {children}
    </motion.div>
  )
}
