import { cn } from '@/utils/cn'

export function DashTitle({ children, className }: { children: string; className?: string }) {
  return (
    <div className={cn('grid grid-cols-[max-content,auto] gap-1.5 text-[#212121] uppercase', className)}>
      <div className='w-[17px] h-0.5 [background:linear-gradient(90deg,rgba(255,0,0,1)_0%,rgba(255,180,0,1)_100%)] place-self-end' />
      <p className='font-medium text-xs tracking-[0] leading-[100%]'>{children}</p>
    </div>
  )
}
