import { cn, dualGradientText } from '@/utils/cn'
import type { ReactNode } from 'react'

export function Title({
  children,
  className,
  as: Component = 'h1',
  ...rest
}: { children: ReactNode; className?: string; as?: 'h1' | 'h2' | 'h3' | 'h4' }) {
  return (
    <Component
      {...rest}
      className={cn(
        dualGradientText,
        'font-bold uppercase inline-block',
        'text-[55px] lte-1024:text-[30px] leading-[100%]',
        className,
      )}
    >
      {children}
    </Component>
  )
}
