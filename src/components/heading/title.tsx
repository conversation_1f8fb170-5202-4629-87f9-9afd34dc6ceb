import { cn, dualGradientText } from '@/utils/cn'
import type { ReactNode } from 'react'

export function Title({
  children,
  className,
  as: Component = 'h1',
  isBigger = false,
  ...rest
}: { children: ReactNode; className?: string; as?: 'h1' | 'h2' | 'h3' | 'h4'; isBigger?: boolean }) {
  return (
    <Component
      {...rest}
      className={cn(
        dualGradientText,
        'font-bold uppercase inline-block',
        isBigger
          ? 'text-[70px] leading-[75px] lte-1024:text-[45px] lte-1024:leading-[50px] lte-375:text-[30px] lte-375:leading-[100%]'
          : 'text-[55px] lte-1024:text-[30px] leading-[100%]',
        className,
      )}
    >
      {children}
    </Component>
  )
}
