import { cn } from '@/utils/cn'

type Props = {
  title: string
  subtitle?: string
  legend?: string
  rows: { left: string; right: string }[]
  variant: 'light' | 'dark'
}

export function SimpleTable({ title, subtitle, legend, rows, variant = 'light' }: Props) {
  return (
    <table className={cn('border w-full border-[#D9D9D9] text-left', variant === 'light' && 'text-[#404040]')}>
      <thead
        className={cn(
          'font-bold text-lg uppercase border-b border-[#D9D9D9]',
          variant === 'dark' && 'bg-[#232323]',
          variant === 'light' && 'bg-[#fafafa]',
        )}
      >
        <tr>
          <th className='py-[10px] px-[12px]'>
            {title}
            {subtitle && (
              <>
                <br />
                {subtitle}
              </>
            )}
          </th>
        </tr>
      </thead>
      <tbody>
        {legend && (
          <tr>
            <td className='px-[12px] pt-[12px]'>{legend}</td>
          </tr>
        )}
        {rows.map((item, index) => (
          <tr key={`${index}-${title}-${subtitle}-${item.left || item.right}`}>
            <td
              className={cn(
                'flex justify-between px-[12px]',
                index === 0 && 'pt-[12px]',
                index === rows.length - 1 && 'pb-[12px]',
              )}
            >
              <span>{item.left}</span>
              <span>{item.right}</span>
            </td>
          </tr>
        ))}
      </tbody>
    </table>
  )
}
