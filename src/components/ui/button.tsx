import { cn, dualGradient } from '@/utils/cn'
import { Slot } from '@radix-ui/react-slot'
import { type VariantProps, cva } from 'class-variance-authority'
import * as React from 'react'

const buttonVariants = cva(
  cn(
    'whitespace-nowrap inline-flex gap-2 items-center justify-center duration-500 uppercase hover:bg-opacity-80 rounded-sm font-bold text-[15px] tracking-[0px] rounded-[4px] h-[50px] px-[30px]',
  ),
  {
    variants: {
      variant: {
        default: 'bg-white text-black',
        gradient: cn(dualGradient, 'text-white'),
        black: 'bg-[#212121] text-[#EEF2F7]',
      },
      hideShadow: {
        true: '',
        false: 'shadow-button',
      },
    },
    defaultVariants: {
      variant: 'default',
    },
  },
)

export interface ButtonProps
  extends React.ButtonHTMLAttributes<HTMLButtonElement>,
    VariantProps<typeof buttonVariants> {
  asChild?: boolean
}

const Button = React.forwardRef<HTMLButtonElement, ButtonProps>(
  ({ className, variant, hideShadow = false, asChild = false, ...props }, ref) => {
    const Comp = asChild ? Slot : 'button'
    return <Comp className={cn(buttonVariants({ variant, hideShadow, className }))} ref={ref} {...props} />
  },
)
Button.displayName = 'Button'

export { Button, buttonVariants }
