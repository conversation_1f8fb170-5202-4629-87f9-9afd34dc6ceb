'use client'

import { Dialog, DialogContent, DialogTitle, DialogTrigger } from '@/components/ui/dialog'
import { ReactNode, useState } from 'react'

export function Modal({ children, trigger }: { children: ReactNode; trigger: ReactNode }) {
  const [open, setOpen] = useState(false)

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogTrigger asChild>{trigger}</DialogTrigger>
      <DialogContent
        className='p-0 bg-white border-none overflow-y-auto  max-w-[1000px] w-[95vw] lte-1024:max-w-[90vw] max-h-[calc(100vh-30px)]'
        onInteractOutside={(e) => {
          e.preventDefault()
          setOpen(false)
        }}
      >
        <button className='absolute top-4 right-4 z-10' onClick={() => setOpen(false)}>
          <img src='./svgs/icons/close-modal.svg' />
        </button>
        <DialogTitle className='hidden' />
        <div className='relative w-full h-full px-[25px] py-[50px]'>{children}</div>
      </DialogContent>
    </Dialog>
  )
}
