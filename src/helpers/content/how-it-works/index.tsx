import { Link } from '@/components/link'
import { ReactNode } from 'react'
import type { HowItWork } from './types'

export const howItWorks: HowItWork[] = [
  {
    title: <p>Select a new laptop & accessories</p>,
    description: (
      <p>Choose a gaming laptop that’s right for you and complete your setup by adding optional accessories. </p>
    ),
  },
  {
    title: <p>Sign up in minutes</p>,
    description: (
      <p>
        Create an account and enter your billing information. Once you’re approved, you’ll receive your gaming laptop
        and accessories within three business days.
        <sup>6</sup>
      </p>
    ),
  },
  {
    title: <p>Enjoy it worry-free</p>,
    description: (
      <p>
        Use your new setup, all for one monthly payment.<sup>1</sup> Dedicated live experts are here around the clock if
        you need help<sup>3</sup>. And if there’s no remote fix, get a new gaming laptop as soon as next business day.
        <sup>4</sup>
      </p>
    ),
  },
  {
    title: (
      <p>
        Flexible Upgrades<sup>1</sup>
      </p>
    ),
    description: (
      <p>
        After one year, you have the option to upgrade to the newest tech—or keep the one you’re already dominating
        with.1 It’s up to you!
      </p>
    ),
  },
]

type ModalStep = {
  steps: {
    title: string
    description: ReactNode
    icon: `./svgs/how-it-works/${string}`
  }[]
}

export const modalSteps: ModalStep = {
  steps: [
    {
      title: 'Try it for 30 days',
      description: (
        <p>
          Your 30-day trial<sup>5</sup> begins the day you sign up.​ If it’s not the right fit, cancel through{' '}
          <Link href='https://hplaptopsubscription.hp.com/self-service/hp-my-account'>MyAccount</Link> within 30 days
          before the trial ends.
        </p>
      ),
      icon: `./svgs/how-it-works/coin-star.svg`,
    },
    {
      title: 'Return the gaming laptop & accessories',
      description: (
        <p>
          We’ll send instructions for returning the laptop and accessories. Return the gaming laptop and any accessories
          within 10 days of receiving the prepaid return label to avoid any non-return fees.{' '}
        </p>
      ),
      icon: `./svgs/how-it-works/package-reload.svg`,
    },
    {
      title: 'Get a refund',
      description: <p>We’ll issue a refund once we receive the gaming laptop and accessories. </p>,
      icon: `./svgs/how-it-works/coin-pig.svg`,
    },
  ],
}
