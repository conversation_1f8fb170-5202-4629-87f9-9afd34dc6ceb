import { links } from '@/utils/links'
import type { Column, FeatureTableItem } from './types'

const GradientCheck = () => <img src='./svgs/circle-check-gradient.svg' className='w-6 h-6' />

const NeutralCheck = () => <img src='./svgs/circle-check.svg' className='w-6 h-6' />

const Dash = () => <div className='w-[19px] h-[1px] bg-neutral-400' />

const Text = ({ children }: { children: string }) => <p className='font-[350] text-neutral-700'>{children}</p>

export const columns: Column[] = [
  {
    title: 'purchase',
    subtitle: 'Pay once',
    highlight: false,
    cta: {
      text: 'SHOP NOW',
      href: 'https://www.hp.com/us-en/gaming-pc/laptops.html',
    },
  },
  {
    title: 'finance',
    subtitle: 'Pay over time',
    highlight: false,
    cta: {
      text: 'LEARN MORE',
      href: 'https://www.hp.com/us-en/shop/cv/hpfinancing',
    },
  },
  {
    title: 'subscribe',
    subtitle: 'Stay up-to-date with the latest tech',
    cta: {
      text: 'GET STARTED',
      href: links.keylightStore,
    },
    highlight: true,
    // badge: 'NEW',
  },
]

export const featuresTable: FeatureTableItem[] = [
  {
    title: <p>NEW OMEN LAPTOP</p>,
    info: 'Your choice of a new Victus or OMEN Gaming laptop.',
    columns: {
      purchase: <NeutralCheck />,
      finance: <NeutralCheck />,
      subscribe: <GradientCheck />,
    },
  },
  {
    title: <p>WILL I OWN THE LAPTOP?</p>,
    info: 'With the OMEN Gaming Subscription, you have access to the latest tech plus 24/7 support, next business day replacement, and upgrades without ownership.',
    columns: {
      purchase: <NeutralCheck />,
      finance: <NeutralCheck />,
      subscribe: <Dash />,
    },
  },
  {
    title: <p>CREDIT CHECK REQUIRED?</p>,
    info: 'The soft credit check assesses your eligibility for the OMEN Gaming Subscription, without impacting your credit score. We do not receive your credit score or personal credit information.',
    columns: {
      purchase: <Text>No</Text>,
      finance: <Text>Yes</Text>,
      subscribe: <Text>Soft credit check</Text>,
    },
  },
  {
    title: <p>Add optional accessories for a low monthly price</p>,
    info: 'You can add our most popular HyperX accessories & OMEN monitor to your subscription for a low monthly price.',
    columns: {
      purchase: <Text>No</Text>,
      finance: <Text>No</Text>,
      subscribe: <GradientCheck />,
    },
  },
  {
    title: <p>30 DAY RISK-FREE TRIAL</p>,
    info: 'If you decide it’s not for you, cancel and return the laptop and accessories within the 30 days for a full refund.',
    columns: {
      purchase: <Dash />,
      finance: <Dash />,
      subscribe: <GradientCheck />,
    },
  },
  {
    title: <p>FLEXIBILITY TO UPGRADE YOUR LAPTOP EVERY YEAR</p>,
    info: 'After 12 months you can return your gaming laptop and upgrade to a new gaming laptop or keep using your current one and continue your current payments.',
    columns: {
      purchase: <Dash />,
      finance: <Dash />,
      subscribe: <GradientCheck />,
    },
  },
  {
    title: <p>WARRANTY</p>,
    info: 'All Victus and OMEN PCs come with a standard one-year warranty. With the OMEN Gaming Subscription, you get 24/7 support with continuous coverage.',
    columns: {
      purchase: <Text>1 year</Text>,
      finance: <Text>1 year</Text>,
      subscribe: <Text>Ongoing</Text>,
    },
  },
  {
    title: (
      <p>
        Next Business Day replacement<sup>4</sup>
      </p>
    ),
    info: "If our dedicated experts can't fix your gaming laptop or accessories, you can get a replacement the next business day. ",
    columns: {
      purchase: <Dash />,
      finance: <Dash />,
      subscribe: <GradientCheck />,
    },
  },
  {
    title: (
      <p>
        24/7 Pro live support with continuous coverage<sup>3</sup>
      </p>
    ),
    info: 'Access to expert support around the clock. Talk to a live expert that can help with any gaming laptop or accessory question or issue. ',
    columns: {
      purchase: <Text>Standard HP support</Text>,
      finance: <Text>Standard HP support</Text>,
      subscribe: <GradientCheck />,
    },
  },
]
