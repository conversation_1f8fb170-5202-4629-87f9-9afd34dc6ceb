import type { FeatureItem } from './types'

export const includedItems: FeatureItem[] = [
  {
    title: 'NEW GAMING LAPTOP + GEAR',
    description: (
      <p>
        Choose a gaming laptop and customize your setup with optional HyperX and OMEN accessories.<sup>1</sup>
      </p>
    ),
    icon: './svgs/what-is-included/laptop.svg',
  },
  {
    title: 'Upgrade after 12 months',
    description: <p>After one year, you can upgrade to the newest tech. Or keep the one you already have.</p>,
    icon: './svgs/what-is-included/laptop-upgrade.svg',
  },
  {
    title: 'Expert 24/7 support',
    description: (
      <p>
        Day or night, our 24/7 Pro live support team is here to help you keep everything working like new.<sup>3</sup>
      </p>
    ),
    icon: './svgs/what-is-included/person-assistant.svg',
  },
  {
    title: 'Next Business Day Replacement',
    description: (
      <p>
        If we can’t fix your gear, we’ll ship a replacement as soon as the next business day, at no extra cost.
        <sup>4</sup>
      </p>
    ),
    icon: './svgs/what-is-included/laptop-replace.svg',
  },
]
