@tailwind base;
@tailwind components;
@tailwind utilities;

/* Gotham - Ultra */
@font-face {
  font-family: "Gotham";
  src: url("/fonts/Gotham-Ultra.otf") format("opentype");
  font-weight: 900;
  font-style: normal;
}
@font-face {
  font-family: "Gotham";
  src: url("/fonts/Gotham-UltraItalic.otf") format("opentype");
  font-weight: 900;
  font-style: italic;
}

/* Gotham - Black */
@font-face {
  font-family: "Gotham";
  src: url("/fonts/Gotham-Black.otf") format("opentype");
  font-weight: 800;
  font-style: normal;
}
@font-face {
  font-family: "Gotham";
  src: url("/fonts/Gotham-BlackItalic.otf") format("opentype");
  font-weight: 800;
  font-style: italic;
}

/* Gotham - Bold */
@font-face {
  font-family: "Gotham";
  src: url("/fonts/Gotham-Bold.otf") format("opentype");
  font-weight: 700;
  font-style: normal;
}
@font-face {
  font-family: "Gotham";
  src: url("/fonts/Gotham-BoldItalic.otf") format("opentype");
  font-weight: 700;
  font-style: italic;
}

/* Gotham - Medium */
@font-face {
  font-family: "Gotham";
  src: url("/fonts/Gotham-Medium.otf") format("opentype");
  font-weight: 500;
  font-style: normal;
}
@font-face {
  font-family: "Gotham";
  src: url("/fonts/Gotham-MediumItalic.otf") format("opentype");
  font-weight: 500;
  font-style: italic;
}

/* Gotham - Book */
@font-face {
  font-family: "Gotham";
  src: url("/fonts/Gotham-Book.otf") format("opentype");
  font-weight: 450;
  font-style: normal;
}
@font-face {
  font-family: "Gotham";
  src: url("/fonts/Gotham-BookItalic.otf") format("opentype");
  font-weight: 450;
  font-style: italic;
}

/* Gotham - Regular / Light */
@font-face {
  font-family: "Gotham";
  src: url("/fonts/Gotham-Light.otf") format("opentype");
  font-weight: 300;
  font-style: normal;
}
@font-face {
  font-family: "Gotham";
  src: url("/fonts/Gotham-LightItalic.otf") format("opentype");
  font-weight: 300;
  font-style: italic;
}

/* Gotham - Thin */
@font-face {
  font-family: "Gotham";
  src: url("/fonts/Gotham-Thin.otf") format("opentype");
  font-weight: 200;
  font-style: normal;
}
@font-face {
  font-family: "Gotham";
  src: url("/fonts/Gotham-ThinItalic.otf") format("opentype");
  font-weight: 200;
  font-style: italic;
}

/* Gotham - XLight */
@font-face {
  font-family: "Gotham";
  src: url("/fonts/Gotham-XLight.otf") format("opentype");
  font-weight: 100;
  font-style: normal;
}
@font-face {
  font-family: "Gotham";
  src: url("/fonts/Gotham-XLightItalic.otf") format("opentype");
  font-weight: 100;
  font-style: italic;
}

* {
  font-family: "Gotham", Helvetica;
}

html {
  @apply overflow-x-hidden;
  scroll-behavior: smooth;
}
