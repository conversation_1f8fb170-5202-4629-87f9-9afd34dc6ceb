<svg width="24" height="14" viewBox="0 0 24 14" fill="none" xmlns="http://www.w3.org/2000/svg">
<g clip-path="url(#clip0_13505_12049)">
<path fill-rule="evenodd" clip-rule="evenodd" d="M0 0L4.17371e-08 3.5L3.42857 3.5L3.42857 -4.08853e-08L0 0ZM20.5714 -2.45312e-07L20.5714 3.5L24 3.5L24 -2.86197e-07L20.5714 -2.45312e-07ZM3.42857 3.5L3.42857 7L6.85714 7L6.85714 3.5L3.42857 3.5ZM17.1429 3.5L17.1429 7L20.5714 7L20.5714 3.5L17.1429 3.5ZM6.85714 7L6.85714 10.5L10.2857 10.5L10.2857 7L6.85714 7ZM13.7143 7L13.7143 10.5L17.1429 10.5L17.1429 7L13.7143 7ZM10.2857 10.5L10.2857 14L13.7143 14L13.7143 10.5L10.2857 10.5Z" fill="url(#paint0_linear_13505_12049)"/>
</g>
<defs>
<linearGradient id="paint0_linear_13505_12049" x1="5.80372e-07" y1="3.5" x2="23.9233" y2="4.85427" gradientUnits="userSpaceOnUse">
<stop stop-color="#FF0100"/>
<stop offset="1" stop-color="#FFB300"/>
</linearGradient>
<clipPath id="clip0_13505_12049">
<rect width="24" height="14" fill="white" transform="matrix(1 0 0 -1 0 14)"/>
</clipPath>
</defs>
</svg>
